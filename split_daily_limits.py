#!/usr/bin/env python3
"""
Script to split CSV rows with Daily Limit > 10,000 into multiple rows of 10,000 each.
The final row contains the remainder (if any).
Rows with Daily Limit ≤ 10,000 are left unchanged.
"""

import pandas as pd
import os
from tqdm import tqdm

def split_daily_limits(input_file, output_file=None):
    """
    Split rows with Daily Limit > 10,000 into multiple rows.
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file (optional)
    """
    
    # Read the CSV file
    print(f"Reading CSV file: {input_file}")
    try:
        df = pd.read_csv(input_file, encoding='utf-8-sig')
    except UnicodeDecodeError:
        df = pd.read_csv(input_file, encoding='utf-8')
    
    print(f"Original file has {len(df)} rows")
    
    # Create a list to store the new rows
    new_rows = []
    
    # Process each row with progress bar
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Processing rows"):
        daily_limit = int(row['Daily Limit'])
        
        if daily_limit <= 10000:
            # Keep the row unchanged
            new_rows.append(row.copy())
        else:
            # Split into multiple rows
            full_chunks = daily_limit // 10000
            remainder = daily_limit % 10000
            
            # Create full chunks of 10,000
            for i in range(full_chunks):
                new_row = row.copy()
                new_row['Daily Limit'] = 10000
                new_rows.append(new_row)
            
            # Add remainder row if there's a remainder
            if remainder > 0:
                new_row = row.copy()
                new_row['Daily Limit'] = remainder
                new_rows.append(new_row)
    
    # Create new DataFrame
    new_df = pd.DataFrame(new_rows)
    
    print(f"New file will have {len(new_df)} rows")
    print(f"Added {len(new_df) - len(df)} additional rows")
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_split.csv"
    
    # Save the new CSV file
    print(f"Saving to: {output_file}")
    new_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Display summary statistics
    print("\n" + "="*50)
    print("SUMMARY")
    print("="*50)
    print(f"Original rows: {len(df)}")
    print(f"New rows: {len(new_df)}")
    print(f"Rows added: {len(new_df) - len(df)}")
    
    # Show examples of splits
    print("\nExamples of rows that were split:")
    original_large = df[df['Daily Limit'] > 10000].head(3)
    for _, row in original_large.iterrows():
        daily_limit = row['Daily Limit']
        full_chunks = daily_limit // 10000
        remainder = daily_limit % 10000
        print(f"- {row['Command Name']}: {daily_limit} → {full_chunks} rows of 10,000", end="")
        if remainder > 0:
            print(f" + 1 row of {remainder}")
        else:
            print()
    
    print(f"\nOutput saved to: {output_file}")
    return output_file

def main():
    """Main function to run the script."""
    input_file = "DBMS_Daily_Campaigns_Commands - Final_Sheet.csv"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found!")
        print("Please make sure the file exists in the current directory.")
        return
    
    try:
        output_file = split_daily_limits(input_file)
        print(f"\n✅ Successfully processed the file!")
        print(f"📁 Output saved as: {output_file}")
        
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
